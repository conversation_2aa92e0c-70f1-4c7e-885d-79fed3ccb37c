import React, { memo, useState, useEffect } from "react";

// Global cache for all loaded states - persists across component unmounts
const globalLoadingCache = {
  backgroundImages: new Set(),
  featureIcons: new Set(),
  componentStates: new Map(),
};

import backgroundImage from "../../assets/Management/Background.svg";
import Icon_01 from "../../assets/Management/Icon_01.svg";
import Icon_02 from "../../assets/Management/Icon_02.svg";
import Icon_03 from "../../assets/Management/Icon_03.svg";
import Icon_04 from "../../assets/Management/Icon_04.svg";
import Icon_05 from "../../assets/Management/Icon_05.svg";
import Icon_06 from "../../assets/Management/Icon_06.svg";
import RightArrow from "../../assets/Management/RightArrow.svg";
import IconHeader from "../../assets/Management/IconHeader.svg";

// Utility function to preload images
const preloadImage = (src) => {
  return new Promise((resolve, reject) => {
    if (globalLoadingCache.featureIcons.has(src)) {
      resolve(true);
      return;
    }

    const img = new Image();
    img.onload = () => {
      globalLoadingCache.featureIcons.add(src);
      resolve(true);
    };
    img.onerror = () => {
      resolve(false); // Don't reject, just resolve with false
    };
    img.src = src;
  });
};

// Memoized feature card component with persistent loading state
const FeatureCard = memo(({ feature, index }) => {
  const [imageLoaded, setImageLoaded] = useState(() => {
    // Check if image was already loaded from global cache
    return typeof feature.icon === "string"
      ? globalLoadingCache.featureIcons.has(feature.icon)
      : true;
  });

  useEffect(() => {
    if (typeof feature.icon === "string" && !imageLoaded) {
      preloadImage(feature.icon).then((loaded) => {
        setImageLoaded(loaded);
      });
    }
  }, [feature.icon, imageLoaded]);

  return (
    <div className="bg-[#ffffff10] w-full md:max-w-[410px] h-auto md:h-[330px] rounded-[20px] p-[20px] text-white transition-colors duration-300 flex flex-col">
      {/* Icon */}
      <div className="flex items-center justify-center mb-6">
        {typeof feature.icon === "string" ? (
          <img
            src={feature.icon}
            alt=""
            className="w-12 h-12"
            decoding="auto"
            width={48}
            height={48}
            style={{
              opacity: imageLoaded ? 1 : 0,
              transition: imageLoaded ? "none" : "opacity 0.3s ease-in-out",
            }}
          />
        ) : (
          feature.icon
        )}
      </div>

      {/* Title */}
      <h3 className="text-[20px] md:text-[16px] font-normal mb-3 leading-tight text-[#FFFFFF]">
        {feature.title}
      </h3>

      {/* Description */}
      <p className="text-[#E4E0DF] text-[16px] md:text-sm mb-6 leading-relaxed flex-grow">
        {feature.description}
      </p>

      {/* Read More Button */}
      <button className="flex items-center justify-between pt-[4px] pb-[4px] pr-[4px] pl-[20px] w-[142px] h-[38px] border border-white/40 rounded-lg text-white text-base font-normal hover:bg-white/10 transition-all duration-200">
        <span>Read More</span>
        <img
          src={RightArrow}
          alt="Right Arrow"
          className="w-[30px] h-[30px]"
          decoding="auto"
          width={30}
          height={30}
        />
      </button>
    </div>
  );
});

FeatureCard.displayName = "FeatureCard";

function ManagementFeatures() {
  // Get unique component ID for this instance
  const componentId = "management-features";

  const [backgroundLoaded, setBackgroundLoaded] = useState(() => {
    // Check if background was already loaded from global cache
    return (
      globalLoadingCache.backgroundImages.has(backgroundImage) ||
      globalLoadingCache.componentStates.get(`${componentId}-background`) ===
        true
    );
  });

  // Preload background image with persistent caching
  useEffect(() => {
    // Check if background is already cached
    if (globalLoadingCache.backgroundImages.has(backgroundImage)) {
      setBackgroundLoaded(true);
      globalLoadingCache.componentStates.set(`${componentId}-background`, true);
      return;
    }

    const img = new Image();
    img.src = backgroundImage;
    img.onload = () => {
      globalLoadingCache.backgroundImages.add(backgroundImage);
      globalLoadingCache.componentStates.set(`${componentId}-background`, true);
      setBackgroundLoaded(true);
    };
    img.onerror = () => {
      globalLoadingCache.componentStates.set(`${componentId}-background`, true);
      setBackgroundLoaded(true); // Still show content even if bg fails
    };
  }, [componentId]);

  // Preload all feature icons on component mount
  useEffect(() => {
    const preloadAllIcons = async () => {
      const iconPromises = featuresData
        .filter((feature) => typeof feature.icon === "string")
        .map((feature) => preloadImage(feature.icon));

      await Promise.all(iconPromises);
    };

    preloadAllIcons();
  }, []);

  const featuresData = [
    {
      icon: Icon_01,
      title: "Plan, Publish and Perform Like a Pro",
      description:
        "Join the platform built for creators, teams, and brands to get their work done.",
    },
    {
      icon: Icon_02,
      title:
        "Post & Schedule across all social media platforms from a single dashboard",
      description:
        "Manage all your social platforms - your content, your brand.",
    },
    {
      icon: Icon_03,
      title:
        "Marketing Tools to help influencers and brands maximize reach and engagement",
      description:
        "Build tools for influencers & brands to grow online reach and boost engagement.",
    },
    {
      icon: Icon_04,
      title:
        "Scheduling & Reports with Performance insights for posts, reels, and audience growth",
      description:
        "Understand your audience, optimize your content, and grow your following.",
    },
    {
      icon: Icon_05,
      title: "Team Access & Roles for content collaboration and approval",
      description:
        "Manage team roles and permissions for seamless content collaboration.",
    },
    {
      icon: Icon_06,
      title: "Comment & Message Management for real-time engagement",
      description:
        "Keep track of comments, replies - reply back, and build relationships.",
    },
  ];

  return (
    <div
      className="py-16 px-4 w-[90%] mx-auto rounded-[20px] bg-cover bg-center bg-no-repeat"
      id="solutions-section"
      style={{
        backgroundImage: backgroundLoaded ? `url(${backgroundImage})` : "none",
        backgroundColor: backgroundLoaded ? "transparent" : "#1a1a1a",
        transition: backgroundLoaded
          ? "none"
          : "background-image 0.3s ease-in-out",
        opacity: 1,
        visibility: "visible",
        minHeight: "600px",
      }}
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Title */}
        <div className="flex flex-col justify-center items-center mb-10">
          <div className="flex justify-center items-center border-[1px] border-[#FFFFFF33] bg-[#FFFFFF1A] w-[104px] h-[38px] p-[10px] rounded-[7px] mb-[14px]">
            <img
              src={IconHeader}
              alt="Icon Header"
              className="w-[18px] h-[18px] mr-2"
              decoding="auto"
              width={18}
              height={18}
            />
            <p className="text-white text-[14px] font-medium">Solutions</p>
          </div>
          <h2 className="text-white text-2xl md:text-3xl font-medium text-center md:mb-12">
            Social Media Management Solutions - Flowkar
          </h2>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mx-8">
          {featuresData.map((feature, index) => (
            <FeatureCard
              key={`feature-${index}`}
              feature={feature}
              index={index}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

export default memo(ManagementFeatures);
